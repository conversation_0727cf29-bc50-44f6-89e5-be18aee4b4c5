# Ứng dụng Tráo Câu Hỏi Word

Ứng dụng web giúp tráo thứ tự câu hỏi trong file Word (.docx) với đáp án được đánh dấu bằng gạch chân.

## Tính năng

- ✅ Upload file Word (.docx)
- ✅ Tự động phân tích câu hỏi và đáp án
- ✅ Nhận diện đáp án đúng bằng định dạng gạch chân
- ✅ Tráo ngẫu nhiên thứ tự câu hỏi
- ✅ Giữ nguyên đáp án đúng trong file kết quả
- ✅ Xem trước câu hỏi trước khi tráo
- ✅ Tải xuống file Word đã tráo

## Cài đặt

### 1. Cài đặt Python dependencies

```bash
pip install -r requirements.txt
```

### 2. Chạy ứng dụng

```bash
python server.py
```

Ứng dụng sẽ chạy tại: http://localhost:5000

## Hướng dẫn sử dụng

### 1. Chuẩn bị file Word

File Word cần có định dạng như sau:

```
1. Câu hỏi đầu tiên?
A. <PERSON><PERSON><PERSON> án A
B. Đáp án B đúng (gạch chân)
C. Đáp án C
D. Đáp án D

2. Câu hỏi thứ hai?
A. Đáp án A đúng (gạch chân)
B. Đáp án B
C. Đáp án C
D. Đáp án D
```

**Lưu ý quan trọng:**
- Mỗi câu hỏi bắt đầu bằng số thứ tự (1., 2., 3...)
- Các đáp án bắt đầu bằng A., B., C., D.
- Đáp án đúng phải được **gạch chân** (underline) trong Word

### 2. Sử dụng ứng dụng

1. Mở trình duyệt và truy cập http://localhost:5000
2. Chọn file Word (.docx) cần tráo
3. Nhấn "Xem trước" để kiểm tra câu hỏi (tùy chọn)
4. Nhấn "Tráo câu hỏi" để xử lý
5. Tải xuống file kết quả

### 3. Kết quả

File Word mới sẽ có:
- Thứ tự câu hỏi được tráo ngẫu nhiên
- Đáp án đúng vẫn được gạch chân
- Thông tin về file gốc và thời gian tạo

## Cấu trúc thư mục

```
├── server.py              # Flask server chính
├── requirements.txt       # Python dependencies
├── templates/
│   └── index.html        # Giao diện web
├── static/
│   ├── css/
│   │   └── style.css     # CSS styling
│   └── js/
│       └── main.js       # JavaScript logic
├── uploads/              # Thư mục lưu file upload tạm thời
├── downloads/            # Thư mục lưu file kết quả
└── README.md            # Hướng dẫn này
```

## Công nghệ sử dụng

- **Backend:** Python Flask
- **Frontend:** HTML, CSS, JavaScript, Bootstrap 5
- **Xử lý Word:** python-docx
- **Icons:** Font Awesome

## Lưu ý

- File upload tối đa 16MB
- Chỉ hỗ trợ file .docx (không hỗ trợ .doc)
- Đáp án đúng phải được gạch chân trong Word
- File tạm thời sẽ được tự động xóa sau khi xử lý

## Khắc phục sự cố

### Lỗi "Không tìm thấy câu hỏi"
- Kiểm tra định dạng câu hỏi (phải bắt đầu bằng số)
- Kiểm tra định dạng đáp án (A., B., C., D.)

### Lỗi "Không tìm thấy đáp án đúng"
- Đảm bảo đáp án đúng được gạch chân trong Word
- Kiểm tra định dạng underline (không phải bold hay italic)

### Lỗi upload file
- Kiểm tra kích thước file (< 16MB)
- Đảm bảo file có định dạng .docx
- Thử lại với file Word mới
