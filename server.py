from flask import Flask, request, render_template, send_file, jsonify, flash, redirect, url_for
from werkzeug.utils import secure_filename
import os
import random
import tempfile
from datetime import datetime
import zipfile
import shutil
from docx import Document
from docx.shared import RGBColor
from docx.enum.text import WD_UNDERLINE
import re

app = Flask(__name__)
app.secret_key = 'your-secret-key-here'

# C<PERSON>u hình upload
UPLOAD_FOLDER = 'uploads'
ALLOWED_EXTENSIONS = {'docx'}
MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB max file size

app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER
app.config['MAX_CONTENT_LENGTH'] = MAX_CONTENT_LENGTH

# Tạo thư mục uploads nếu chưa có
os.makedirs(UPLOAD_FOLDER, exist_ok=True)
os.makedirs('downloads', exist_ok=True)

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def debug_document_structure(doc):
    """
    Debug function để hiển thị cấu trúc document
    """
    print("=== DOCUMENT STRUCTURE DEBUG ===")
    for i, paragraph in enumerate(doc.paragraphs):
        text = paragraph.text.strip()
        if text:
            print(f"Paragraph {i}: '{text}'")
            for j, run in enumerate(paragraph.runs):
                if run.text.strip():
                    underline_info = f"Underline: {run.underline}"
                    if hasattr(run, 'font') and hasattr(run.font, 'underline'):
                        underline_info += f", Font.underline: {run.font.underline}"
                    print(f"  Run {j}: '{run.text}' - {underline_info}")
    print("=== END DEBUG ===")

def extract_questions_and_answers_v2(doc):
    """
    Phiên bản cải tiến để trích xuất câu hỏi và đáp án
    """
    questions = []
    paragraphs = [p for p in doc.paragraphs if p.text.strip()]

    i = 0
    while i < len(paragraphs):
        text = paragraphs[i].text.strip()

        # Tìm câu hỏi (bắt đầu bằng số)
        question_match = re.match(r'^(\d+)\.\s*(.+)', text)
        if question_match:
            question_num = question_match.group(1)
            question_text = question_match.group(2)

            # Tìm các đáp án tiếp theo
            answers = []
            correct_answer = ""
            i += 1

            # Đọc các đáp án A, B, C, D
            while i < len(paragraphs):
                answer_text = paragraphs[i].text.strip()
                answer_match = re.match(r'^([A-D])\.\s*(.+)', answer_text)

                if answer_match:
                    option_letter = answer_match.group(1)
                    option_text = answer_match.group(2)
                    full_answer = f"{option_letter}. {option_text}"
                    answers.append(full_answer)

                    # Kiểm tra underline
                    for run in paragraphs[i].runs:
                        if (run.underline == True or run.underline == 1 or
                            (hasattr(run, 'font') and hasattr(run.font, 'underline') and run.font.underline)):
                            correct_answer = option_letter
                            break

                    i += 1
                else:
                    # Không phải đáp án nữa, break
                    break

            # Lưu câu hỏi nếu có đáp án
            if answers:
                question_obj = {
                    "text": f"{question_num}. {question_text}",
                    "answers": answers,
                    "correct_answer": correct_answer
                }
                questions.append(question_obj)
                print(f"Tìm thấy câu hỏi {question_num}: {len(answers)} đáp án, đáp án đúng: {correct_answer}")
        else:
            i += 1

    # Lọc câu hỏi hợp lệ
    valid_questions = [q for q in questions if len(q["answers"]) >= 2 and q["correct_answer"]]
    print(f"Tổng câu hỏi hợp lệ: {len(valid_questions)}")

    return valid_questions

def extract_questions_and_answers_v3(doc):
    """
    Phiên bản v3 - xử lý file Word với format đặc biệt
    """
    questions = []
    paragraphs = [p for p in doc.paragraphs if p.text.strip()]

    i = 0
    question_count = 0

    while i < len(paragraphs):
        text = paragraphs[i].text.strip()

        # Tìm pattern đáp án A, B, C, D để xác định vùng câu hỏi
        if re.match(r'^[A-D]\.\s*', text):
            # Tìm ngược lên để tìm câu hỏi
            question_text = ""
            j = i - 1

            # Tìm ngược lên để lấy text câu hỏi (bỏ qua các đoạn trống)
            while j >= 0:
                prev_text = paragraphs[j].text.strip()
                if prev_text and not re.match(r'^[A-D]\.\s*', prev_text):
                    if question_text:
                        question_text = prev_text + " " + question_text
                    else:
                        question_text = prev_text
                    j -= 1
                else:
                    break

            if question_text:
                question_count += 1
                answers = []
                correct_answer = ""

                # Thu thập tất cả đáp án A, B, C, D
                while i < len(paragraphs):
                    answer_text = paragraphs[i].text.strip()
                    answer_match = re.match(r'^([A-D])\.\s*(.+)', answer_text)

                    if answer_match:
                        option_letter = answer_match.group(1)
                        full_answer = answer_text
                        answers.append(full_answer)

                        # Kiểm tra underline
                        for run in paragraphs[i].runs:
                            if (run.underline == True or run.underline == 1 or
                                (hasattr(run, 'font') and hasattr(run.font, 'underline') and run.font.underline)):
                                correct_answer = option_letter
                                break

                        i += 1
                    else:
                        break

                # Lưu câu hỏi nếu có đáp án
                if answers and len(answers) >= 2:
                    question_obj = {
                        "text": f"{question_count}. {question_text}",
                        "answers": answers,
                        "correct_answer": correct_answer
                    }
                    questions.append(question_obj)
                    print(f"V3 - Tìm thấy câu hỏi {question_count}: {len(answers)} đáp án, đáp án đúng: {correct_answer}")
        else:
            i += 1

    # Lọc câu hỏi hợp lệ
    valid_questions = [q for q in questions if len(q["answers"]) >= 2 and q["correct_answer"]]
    print(f"V3 - Tổng câu hỏi hợp lệ: {len(valid_questions)}")

    return valid_questions

def extract_questions_and_answers_v4(doc):
    """
    Phiên bản v4 - thuật toán mạnh nhất, kết hợp nhiều phương pháp
    """
    questions = []
    paragraphs = [p for p in doc.paragraphs if p.text.strip()]

    print(f"V4 - Bắt đầu phân tích {len(paragraphs)} paragraphs")

    i = 0
    while i < len(paragraphs):
        text = paragraphs[i].text.strip()

        # Phương pháp 1: Tìm câu hỏi có số thứ tự
        question_match = re.match(r'^(\d+)\.\s*(.+)', text)
        if question_match:
            question_num = question_match.group(1)
            question_text = question_match.group(2)

            # Tìm các đáp án tiếp theo
            answers = []
            correct_answer = ""
            i += 1

            # Đọc các đáp án A, B, C, D
            while i < len(paragraphs):
                answer_text = paragraphs[i].text.strip()
                answer_match = re.match(r'^([A-D])\.\s*(.+)', answer_text)

                if answer_match:
                    option_letter = answer_match.group(1)
                    answers.append(answer_text)

                    # Kiểm tra underline
                    for run in paragraphs[i].runs:
                        if (run.underline == True or run.underline == 1 or
                            (hasattr(run, 'font') and hasattr(run.font, 'underline') and run.font.underline)):
                            correct_answer = option_letter
                            break

                    i += 1
                else:
                    # Không phải đáp án nữa, break
                    break

            # Lưu câu hỏi nếu có đáp án
            if answers and len(answers) >= 2:
                question_obj = {
                    "text": f"{question_num}. {question_text}",
                    "answers": answers,
                    "correct_answer": correct_answer
                }
                questions.append(question_obj)
                print(f"V4 - Phương pháp 1: Câu {question_num}, {len(answers)} đáp án, đúng: {correct_answer}")
            continue

        # Phương pháp 2: Tìm từ đáp án A ngược lên (cho format đặc biệt)
        if re.match(r'^A\.\s*', text):
            # Tìm ngược lên để tìm câu hỏi
            question_text = ""
            j = i - 1

            # Tìm ngược lên để lấy text câu hỏi
            question_parts = []
            while j >= 0:
                prev_text = paragraphs[j].text.strip()
                if prev_text and not re.match(r'^[A-D]\.\s*', prev_text):
                    # Kiểm tra xem có phải là câu hỏi không (kết thúc bằng dấu hỏi hoặc dấu hai chấm)
                    if prev_text.endswith('?') or prev_text.endswith(':') or len(prev_text) > 20:
                        question_parts.insert(0, prev_text)
                        j -= 1
                    else:
                        question_parts.insert(0, prev_text)
                        j -= 1
                        if len(question_parts) >= 3:  # Giới hạn để tránh lấy quá nhiều
                            break
                else:
                    break

            question_text = " ".join(question_parts)

            if question_text and len(question_text) > 10:  # Đảm bảo có câu hỏi hợp lệ
                answers = []
                correct_answer = ""

                # Thu thập tất cả đáp án A, B, C, D
                while i < len(paragraphs):
                    answer_text = paragraphs[i].text.strip()
                    answer_match = re.match(r'^([A-D])\.\s*(.+)', answer_text)

                    if answer_match:
                        option_letter = answer_match.group(1)
                        answers.append(answer_text)

                        # Kiểm tra underline
                        for run in paragraphs[i].runs:
                            if (run.underline == True or run.underline == 1 or
                                (hasattr(run, 'font') and hasattr(run.font, 'underline') and run.font.underline)):
                                correct_answer = option_letter
                                break

                        i += 1
                    else:
                        break

                # Lưu câu hỏi nếu có đáp án
                if answers and len(answers) >= 2:
                    question_count = len(questions) + 1
                    question_obj = {
                        "text": f"{question_count}. {question_text}",
                        "answers": answers,
                        "correct_answer": correct_answer
                    }
                    questions.append(question_obj)
                    print(f"V4 - Phương pháp 2: Câu {question_count}, {len(answers)} đáp án, đúng: {correct_answer}")
                continue

        i += 1

    # Loại bỏ trùng lặp dựa trên nội dung câu hỏi
    unique_questions = []
    seen_questions = set()

    for q in questions:
        # Tạo key duy nhất từ 50 ký tự đầu của câu hỏi
        question_key = q["text"][:50].lower().strip()
        if question_key not in seen_questions:
            seen_questions.add(question_key)
            unique_questions.append(q)
        else:
            print(f"V4 - Bỏ qua câu hỏi trùng lặp: {q['text'][:30]}...")

    # Lọc câu hỏi hợp lệ (có đáp án đúng)
    valid_questions = []
    for q in unique_questions:
        if len(q["answers"]) >= 2:
            if q["correct_answer"]:
                valid_questions.append(q)
                print(f"V4 - Câu hỏi hợp lệ: {len(q['answers'])} đáp án, đúng: {q['correct_answer']}")
            else:
                print(f"V4 - Bỏ qua câu không có đáp án đúng: {q['text'][:30]}...")
        else:
            print(f"V4 - Bỏ qua câu không đủ đáp án: {q['text'][:30]}...")

    print(f"V4 - Tổng câu hỏi tìm được: {len(questions)}")
    print(f"V4 - Câu hỏi duy nhất: {len(unique_questions)}")
    print(f"V4 - Câu hỏi hợp lệ: {len(valid_questions)}")

    return valid_questions

def extract_questions_and_answers(doc):
    """
    Trích xuất câu hỏi và đáp án từ document Word
    Đáp án được xác định bằng text gạch chân
    """
    questions = []
    current_question = {"text": "", "answers": [], "correct_answer": ""}

    print(f"Tổng số paragraphs: {len(doc.paragraphs)}")

    for i, paragraph in enumerate(doc.paragraphs):
        text = paragraph.text.strip()
        if not text:
            continue

        print(f"Processing paragraph {i}: '{text}'")

        # Kiểm tra xem có phải câu hỏi mới không (bắt đầu bằng số)
        question_match = re.match(r'^(\d+)\.\s*(.+)', text)
        if question_match:
            print(f"  -> Tìm thấy câu hỏi mới: {question_match.group(1)}")

            # Lưu câu hỏi trước đó nếu có và có đáp án
            if current_question["text"] and current_question["answers"]:
                print(f"  -> Lưu câu hỏi trước: {len(current_question['answers'])} đáp án")
                questions.append(current_question.copy())

            # Bắt đầu câu hỏi mới
            question_number = question_match.group(1)
            question_text = question_match.group(2)
            current_question = {
                "text": f"{question_number}. {question_text}",
                "answers": [],
                "correct_answer": ""
            }

        # Kiểm tra các đáp án (A, B, C, D)
        elif re.match(r'^[A-D]\.\s*', text):
            if current_question["text"]:  # Chỉ thêm đáp án nếu đã có câu hỏi
                print(f"  -> Tìm thấy đáp án: {text[0]}")

                # Kiểm tra xem có text gạch chân không
                is_correct = False
                for run in paragraph.runs:
                    if run.underline == True or run.underline == 1:
                        is_correct = True
                        current_question["correct_answer"] = text[0]  # Lấy chữ cái A, B, C, D
                        print(f"    -> Đáp án đúng: {text[0]}")
                        break

                current_question["answers"].append(text)
            else:
                print(f"  -> Bỏ qua đáp án vì chưa có câu hỏi: {text}")

        # Nếu không phải câu hỏi hoặc đáp án, có thể là phần tiếp theo của câu hỏi hiện tại
        elif current_question["text"] and not re.match(r'^[A-D]\.\s*', text) and not re.match(r'^\d+\.\s*', text):
            # Chỉ thêm vào câu hỏi nếu chưa có đáp án nào
            if not current_question["answers"]:
                print(f"  -> Thêm vào câu hỏi hiện tại: {text}")
                current_question["text"] += " " + text
            else:
                print(f"  -> Bỏ qua text vì đã có đáp án: {text}")
        else:
            print(f"  -> Bỏ qua paragraph: {text}")

    # Lưu câu hỏi cuối cùng nếu có đáp án
    if current_question["text"] and current_question["answers"]:
        print(f"Lưu câu hỏi cuối: {len(current_question['answers'])} đáp án")
        questions.append(current_question)

    print(f"Tổng số câu hỏi tìm được: {len(questions)}")

    # Lọc bỏ các câu hỏi không hợp lệ (không có đủ đáp án hoặc không có đáp án đúng)
    valid_questions = []
    for i, q in enumerate(questions):
        if len(q["answers"]) >= 2:  # Ít nhất 2 đáp án
            if q["correct_answer"]:
                print(f"Câu hỏi {i+1} hợp lệ: {len(q['answers'])} đáp án, đáp án đúng: {q['correct_answer']}")
                valid_questions.append(q)
            else:
                print(f"Câu hỏi {i+1} không có đáp án đúng (gạch chân)")
        else:
            print(f"Câu hỏi {i+1} không đủ đáp án: {len(q['answers'])}")

    print(f"Số câu hỏi hợp lệ: {len(valid_questions)}")
    return valid_questions

def shuffle_questions(questions, shuffle_mode='all', question_count=None, start_from=1):
    """
    Tráo thứ tự câu hỏi theo tùy chọn

    Args:
        questions: Danh sách câu hỏi
        shuffle_mode: 'all' hoặc 'custom'
        question_count: Số câu hỏi muốn tráo (chỉ dùng khi mode='custom')
        start_from: Bắt đầu từ câu số (chỉ dùng khi mode='custom')
    """
    if shuffle_mode == 'all':
        # Tráo tất cả câu hỏi
        shuffled = questions.copy()
        random.shuffle(shuffled)
        return shuffled

    elif shuffle_mode == 'custom':
        # Tráo một phần câu hỏi
        total = len(questions)
        start_index = max(0, start_from - 1)  # Convert to 0-based index
        count = min(question_count or total, total - start_index)

        print(f"Tráo custom: {count} câu từ vị trí {start_index + 1}")

        # Copy danh sách gốc
        result = questions.copy()

        # Lấy phần cần tráo
        section_to_shuffle = result[start_index:start_index + count]

        # Tráo phần đó
        random.shuffle(section_to_shuffle)

        # Thay thế vào danh sách kết quả
        result[start_index:start_index + count] = section_to_shuffle

        return result

    return questions

def create_shuffled_document(questions, original_filename):
    """Tạo document mới với câu hỏi đã tráo"""
    doc = Document()

    # Thêm tiêu đề
    title = doc.add_heading('Đề thi đã tráo câu hỏi', 0)

    # Thêm thông tin
    info_para = doc.add_paragraph()
    info_para.add_run(f'File gốc: {original_filename}\n')
    info_para.add_run(f'Thời gian tạo: {datetime.now().strftime("%d/%m/%Y %H:%M:%S")}\n')
    info_para.add_run(f'Tổng số câu hỏi: {len(questions)}')

    doc.add_paragraph()  # Dòng trống

    # Thêm các câu hỏi đã tráo
    for i, question in enumerate(questions, 1):
        # Thêm câu hỏi với số thứ tự mới
        question_text = re.sub(r'^\d+\.', f'{i}.', question["text"])
        question_para = doc.add_paragraph()
        question_para.add_run(question_text).bold = True

        # Thêm các đáp án
        for answer in question["answers"]:
            answer_para = doc.add_paragraph()
            answer_run = answer_para.add_run(answer)

            # Gạch chân đáp án đúng
            if answer.startswith(question["correct_answer"] + '.'):
                answer_run.underline = WD_UNDERLINE.SINGLE

    return doc

@app.route('/')
def index():
    return render_template('index.html')

@app.route('/upload', methods=['POST'])
def upload_file():
    try:
        if 'file' not in request.files:
            return jsonify({'error': 'Không có file được chọn'}), 400

        file = request.files['file']
        if file.filename == '':
            return jsonify({'error': 'Không có file được chọn'}), 400

        if file and allowed_file(file.filename):
            filename = secure_filename(file.filename)
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{timestamp}_{filename}"
            filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
            file.save(filepath)

            # Xử lý file Word
            try:
                doc = Document(filepath)
                # Debug document structure
                debug_document_structure(doc)

                # Thử phiên bản v4 mạnh nhất trước
                questions = extract_questions_and_answers_v4(doc)

                # Nếu không tìm thấy đủ câu hỏi, thử các phiên bản khác
                if len(questions) < 10:
                    print("Phiên bản v4 tìm thấy ít câu hỏi, thử phiên bản v3...")
                    questions_v3 = extract_questions_and_answers_v3(doc)
                    if len(questions_v3) > len(questions):
                        questions = questions_v3

                if len(questions) < 10:
                    print("Thử phiên bản v2...")
                    questions_v2 = extract_questions_and_answers_v2(doc)
                    if len(questions_v2) > len(questions):
                        questions = questions_v2

                # Nếu vẫn không tìm thấy câu hỏi, thử phiên bản cũ
                if not questions:
                    print("Thử phiên bản cũ...")
                    questions = extract_questions_and_answers(doc)

                if not questions:
                    return jsonify({'error': 'Không tìm thấy câu hỏi nào trong file'}), 400

                # Lấy tùy chọn tráo từ form
                shuffle_mode = request.form.get('shuffle_mode', 'all')
                question_count = None
                start_from = 1

                if shuffle_mode == 'custom':
                    try:
                        question_count = int(request.form.get('question_count', len(questions)))
                        start_from = int(request.form.get('start_from', 1))
                    except (ValueError, TypeError):
                        question_count = len(questions)
                        start_from = 1

                print(f"Shuffle mode: {shuffle_mode}, count: {question_count}, start: {start_from}")

                # Tráo câu hỏi
                shuffled_questions = shuffle_questions(questions, shuffle_mode, question_count, start_from)

                # Tạo document mới
                new_doc = create_shuffled_document(shuffled_questions, file.filename)

                # Lưu file mới
                output_filename = f"shuffled_{timestamp}_{file.filename}"
                output_path = os.path.join('downloads', output_filename)
                new_doc.save(output_path)

                # Xóa file upload tạm thời
                os.remove(filepath)

                # Tạo thông báo kết quả
                if shuffle_mode == 'all':
                    message = f'Đã tráo thành công tất cả {len(questions)} câu hỏi'
                else:
                    actual_count = min(question_count, len(questions) - start_from + 1)
                    message = f'Đã tráo thành công {actual_count} câu hỏi (từ câu {start_from} đến câu {start_from + actual_count - 1})'

                return jsonify({
                    'success': True,
                    'message': message,
                    'download_url': f'/download/{output_filename}',
                    'original_count': len(questions),
                    'shuffled_count': actual_count if shuffle_mode == 'custom' else len(questions),
                    'shuffle_mode': shuffle_mode,
                    'filename': output_filename
                })

            except Exception as e:
                # Xóa file upload nếu có lỗi
                if os.path.exists(filepath):
                    os.remove(filepath)
                return jsonify({'error': f'Lỗi xử lý file: {str(e)}'}), 500

        return jsonify({'error': 'File không hợp lệ. Chỉ chấp nhận file .docx'}), 400

    except Exception as e:
        return jsonify({'error': f'Lỗi server: {str(e)}'}), 500

@app.route('/download/<filename>')
def download_file(filename):
    try:
        file_path = os.path.join('downloads', filename)
        if os.path.exists(file_path):
            return send_file(file_path, as_attachment=True, download_name=filename)
        else:
            return jsonify({'error': 'File không tồn tại'}), 404
    except Exception as e:
        return jsonify({'error': f'Lỗi download: {str(e)}'}), 500

@app.route('/preview', methods=['POST'])
def preview_questions():
    try:
        if 'file' not in request.files:
            return jsonify({'error': 'Không có file được chọn'}), 400

        file = request.files['file']
        if file.filename == '':
            return jsonify({'error': 'Không có file được chọn'}), 400

        if file and allowed_file(file.filename):
            # Lưu file tạm thời
            filename = secure_filename(file.filename)
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            temp_filename = f"temp_{timestamp}_{filename}"
            temp_filepath = os.path.join(app.config['UPLOAD_FOLDER'], temp_filename)
            file.save(temp_filepath)

            try:
                doc = Document(temp_filepath)
                # Debug document structure
                debug_document_structure(doc)

                # Thử phiên bản v4 mạnh nhất trước
                questions = extract_questions_and_answers_v4(doc)

                # Nếu không tìm thấy đủ câu hỏi, thử các phiên bản khác
                if len(questions) < 10:
                    print("Phiên bản v4 tìm thấy ít câu hỏi, thử phiên bản v3...")
                    questions_v3 = extract_questions_and_answers_v3(doc)
                    if len(questions_v3) > len(questions):
                        questions = questions_v3

                if len(questions) < 10:
                    print("Thử phiên bản v2...")
                    questions_v2 = extract_questions_and_answers_v2(doc)
                    if len(questions_v2) > len(questions):
                        questions = questions_v2

                # Nếu vẫn không tìm thấy câu hỏi, thử phiên bản cũ
                if not questions:
                    print("Thử phiên bản cũ...")
                    questions = extract_questions_and_answers(doc)

                # Xóa file tạm thời
                os.remove(temp_filepath)

                if not questions:
                    return jsonify({'error': 'Không tìm thấy câu hỏi nào trong file'}), 400

                # Trả về preview
                preview_data = []
                for i, q in enumerate(questions[:5], 1):  # Chỉ hiển thị 5 câu đầu
                    preview_data.append({
                        'number': i,
                        'text': q['text'],
                        'answers': q['answers'],
                        'correct_answer': q['correct_answer']
                    })

                return jsonify({
                    'success': True,
                    'total_questions': len(questions),
                    'preview': preview_data,
                    'has_more': len(questions) > 5
                })

            except Exception as e:
                # Xóa file tạm thời nếu có lỗi
                if os.path.exists(temp_filepath):
                    os.remove(temp_filepath)
                return jsonify({'error': f'Lỗi xử lý file: {str(e)}'}), 500

        return jsonify({'error': 'File không hợp lệ. Chỉ chấp nhận file .docx'}), 400

    except Exception as e:
        return jsonify({'error': f'Lỗi server: {str(e)}'}), 500

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)