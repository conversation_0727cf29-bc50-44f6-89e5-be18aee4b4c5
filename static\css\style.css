/* Custom styles for Question Shuffler */

body {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.card {
    border: none;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.card-header {
    border-radius: 15px 15px 0 0 !important;
    border: none;
}

.btn {
    border-radius: 25px;
    padding: 10px 25px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.form-control {
    border-radius: 10px;
    border: 2px solid #e9ecef;
    padding: 12px 15px;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.alert {
    border-radius: 10px;
    border: none;
}

.spinner-border {
    width: 3rem;
    height: 3rem;
}

/* Preview styles */
.question-preview {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 15px;
    margin-bottom: 15px;
    border-left: 4px solid #667eea;
}

.question-text {
    font-weight: 600;
    color: #333;
    margin-bottom: 10px;
}

.answer-option {
    padding: 5px 0;
    color: #666;
}

.correct-answer {
    color: #28a745;
    font-weight: 600;
    text-decoration: underline;
}

/* Animation for loading */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

/* File input styling */
.form-control[type="file"] {
    padding: 8px 12px;
}

.form-control[type="file"]::-webkit-file-upload-button {
    background: #667eea;
    color: white;
    border: none;
    border-radius: 20px;
    padding: 8px 15px;
    margin-right: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.form-control[type="file"]::-webkit-file-upload-button:hover {
    background: #5a6fd8;
}

/* Success animation */
@keyframes checkmark {
    0% { transform: scale(0); }
    50% { transform: scale(1.2); }
    100% { transform: scale(1); }
}

.success-icon {
    animation: checkmark 0.6s ease-in-out;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .container {
        margin-top: 20px;
    }
    
    .card-body {
        padding: 20px;
    }
    
    .btn {
        width: 100%;
        margin-bottom: 10px;
    }
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 10px;
}

::-webkit-scrollbar-thumb {
    background: #667eea;
    border-radius: 10px;
}

::-webkit-scrollbar-thumb:hover {
    background: #5a6fd8;
}
