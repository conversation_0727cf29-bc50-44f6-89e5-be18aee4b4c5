<!DOCTYPE html>
<html lang="vi">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Tráo Câu Hỏi Word</title>
    <link
      href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css"
      rel="stylesheet"
    />
    <link
      href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css"
      rel="stylesheet"
    />
    <link
      href="{{ url_for('static', filename='css/style.css') }}"
      rel="stylesheet"
    />
  </head>
  <body>
    <div class="container mt-5">
      <div class="row justify-content-center">
        <div class="col-md-8">
          <div class="card shadow">
            <div class="card-header bg-primary text-white text-center">
              <h2><i class="fas fa-shuffle me-2"></i>Tráo Câu Hỏi Word</h2>
              <p class="mb-0">
                Upload file Word để tráo thứ tự câu hỏi (đáp án gạch chân sẽ
                được giữ nguyên)
              </p>
            </div>
            <div class="card-body">
              <!-- Form Upload -->
              <form id="uploadForm" enctype="multipart/form-data">
                <div class="mb-4">
                  <label for="fileInput" class="form-label">
                    <i class="fas fa-file-word me-2"></i>Chọn file Word (.docx)
                  </label>
                  <input
                    type="file"
                    class="form-control"
                    id="fileInput"
                    name="file"
                    accept=".docx"
                    required
                  />
                  <div class="form-text">
                    <i class="fas fa-info-circle me-1"></i>
                    File phải có định dạng .docx và chứa câu hỏi với đáp án gạch
                    chân
                  </div>
                </div>

                <div class="d-grid gap-2 d-md-flex justify-content-md-center">
                  <button
                    type="button"
                    class="btn btn-outline-primary"
                    id="previewBtn"
                  >
                    <i class="fas fa-eye me-2"></i>Xem trước
                  </button>
                  <button type="submit" class="btn btn-primary">
                    <i class="fas fa-shuffle me-2"></i>Tráo câu hỏi
                  </button>
                </div>
              </form>

              <!-- Loading -->
              <div id="loading" class="text-center mt-4" style="display: none">
                <div class="spinner-border text-primary" role="status">
                  <span class="visually-hidden">Đang xử lý...</span>
                </div>
                <p class="mt-2">Đang xử lý file, vui lòng đợi...</p>
              </div>

              <!-- Alert Messages -->
              <div id="alertContainer" class="mt-4"></div>

              <!-- Preview Section -->
              <div id="previewSection" class="mt-4" style="display: none">
                <div class="card">
                  <div class="card-header">
                    <h5><i class="fas fa-eye me-2"></i>Xem trước câu hỏi</h5>
                  </div>
                  <div class="card-body">
                    <div id="previewContent"></div>
                  </div>
                </div>
              </div>

              <!-- Result Section -->
              <div id="resultSection" class="mt-4" style="display: none">
                <div class="card border-success">
                  <div class="card-header bg-success text-white">
                    <h5><i class="fas fa-check-circle me-2"></i>Kết quả</h5>
                  </div>
                  <div class="card-body">
                    <div id="resultContent"></div>
                    <div
                      class="d-grid gap-2 d-md-flex justify-content-md-center mt-3"
                    >
                      <a
                        id="downloadBtn"
                        href="#"
                        class="btn btn-success"
                        style="display: none"
                      >
                        <i class="fas fa-download me-2"></i>Tải xuống file đã
                        tráo
                      </a>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Instructions -->
          <div class="card mt-4">
            <div class="card-header">
              <h5>
                <i class="fas fa-question-circle me-2"></i>Hướng dẫn sử dụng
              </h5>
            </div>
            <div class="card-body">
              <ol>
                <li>
                  <strong>Chuẩn bị file Word:</strong> File phải có định dạng
                  .docx
                </li>
                <li>
                  <strong>Định dạng câu hỏi:</strong> Mỗi câu hỏi bắt đầu bằng
                  số thứ tự (1., 2., 3...)
                </li>
                <li>
                  <strong>Định dạng đáp án:</strong> Các đáp án bắt đầu bằng A.,
                  B., C., D.
                </li>
                <li>
                  <strong>Đánh dấu đáp án đúng:</strong> Gạch chân (underline)
                  đáp án đúng trong Word
                </li>
                <li>
                  <strong>Upload và tráo:</strong> Chọn file và nhấn "Tráo câu
                  hỏi"
                </li>
                <li>
                  <strong>Tải xuống:</strong> File mới sẽ có thứ tự câu hỏi được
                  tráo ngẫu nhiên
                </li>
              </ol>

              <div class="alert alert-info mt-3">
                <i class="fas fa-lightbulb me-2"></i>
                <strong>Lưu ý:</strong> Đáp án đúng (được gạch chân) sẽ được giữ
                nguyên trong file kết quả.
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="{{ url_for('static', filename='js/main.js') }}"></script>
  </body>
</html>
