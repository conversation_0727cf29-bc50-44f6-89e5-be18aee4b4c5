// Main JavaScript for Question Shuffler

document.addEventListener('DOMContentLoaded', function() {
    const uploadForm = document.getElementById('uploadForm');
    const fileInput = document.getElementById('fileInput');
    const previewBtn = document.getElementById('previewBtn');
    const loading = document.getElementById('loading');
    const alertContainer = document.getElementById('alertContainer');
    const previewSection = document.getElementById('previewSection');
    const previewContent = document.getElementById('previewContent');
    const resultSection = document.getElementById('resultSection');
    const resultContent = document.getElementById('resultContent');
    const downloadBtn = document.getElementById('downloadBtn');

    // Show alert function
    function showAlert(message, type = 'danger') {
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show fade-in`;
        alertDiv.innerHTML = `
            <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-triangle'} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        alertContainer.innerHTML = '';
        alertContainer.appendChild(alertDiv);
        
        // Auto dismiss after 5 seconds
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, 5000);
    }

    // Show loading
    function showLoading() {
        loading.style.display = 'block';
        previewSection.style.display = 'none';
        resultSection.style.display = 'none';
        alertContainer.innerHTML = '';
    }

    // Hide loading
    function hideLoading() {
        loading.style.display = 'none';
    }

    // Validate file
    function validateFile() {
        const file = fileInput.files[0];
        if (!file) {
            showAlert('Vui lòng chọn file Word (.docx)');
            return false;
        }

        if (!file.name.toLowerCase().endsWith('.docx')) {
            showAlert('File phải có định dạng .docx');
            return false;
        }

        if (file.size > 16 * 1024 * 1024) { // 16MB
            showAlert('File quá lớn. Kích thước tối đa là 16MB');
            return false;
        }

        return true;
    }

    // Preview questions
    previewBtn.addEventListener('click', function() {
        if (!validateFile()) return;

        showLoading();
        
        const formData = new FormData();
        formData.append('file', fileInput.files[0]);

        fetch('/preview', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            hideLoading();
            
            if (data.success) {
                displayPreview(data);
                showAlert(`Tìm thấy ${data.total_questions} câu hỏi trong file`, 'success');
            } else {
                showAlert(data.error || 'Có lỗi xảy ra khi xem trước file');
            }
        })
        .catch(error => {
            hideLoading();
            showAlert('Lỗi kết nối: ' + error.message);
        });
    });

    // Display preview
    function displayPreview(data) {
        let html = `
            <div class="mb-3">
                <strong>Tổng số câu hỏi:</strong> ${data.total_questions}
                ${data.has_more ? '<br><em>(Hiển thị 5 câu đầu tiên)</em>' : ''}
            </div>
        `;

        data.preview.forEach(question => {
            html += `
                <div class="question-preview">
                    <div class="question-text">${question.text}</div>
                    <div class="answers">
                        ${question.answers.map(answer => `
                            <div class="answer-option ${answer.startsWith(question.correct_answer + '.') ? 'correct-answer' : ''}">
                                ${answer}
                            </div>
                        `).join('')}
                    </div>
                </div>
            `;
        });

        previewContent.innerHTML = html;
        previewSection.style.display = 'block';
        previewSection.classList.add('fade-in');
    }

    // Upload and shuffle
    uploadForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        if (!validateFile()) return;

        showLoading();
        
        const formData = new FormData();
        formData.append('file', fileInput.files[0]);

        fetch('/upload', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            hideLoading();
            
            if (data.success) {
                displayResult(data);
                showAlert(data.message, 'success');
            } else {
                showAlert(data.error || 'Có lỗi xảy ra khi xử lý file');
            }
        })
        .catch(error => {
            hideLoading();
            showAlert('Lỗi kết nối: ' + error.message);
        });
    });

    // Display result
    function displayResult(data) {
        const html = `
            <div class="text-center">
                <i class="fas fa-check-circle text-success success-icon" style="font-size: 3rem;"></i>
                <h4 class="mt-3">Tráo câu hỏi thành công!</h4>
                <p class="mb-3">${data.message}</p>
                <div class="row text-center">
                    <div class="col-md-6">
                        <div class="card bg-light">
                            <div class="card-body">
                                <h5 class="card-title">Số câu hỏi</h5>
                                <h3 class="text-primary">${data.original_count}</h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card bg-light">
                            <div class="card-body">
                                <h5 class="card-title">File kết quả</h5>
                                <p class="text-muted small">${data.filename}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        resultContent.innerHTML = html;
        downloadBtn.href = data.download_url;
        downloadBtn.style.display = 'inline-block';
        resultSection.style.display = 'block';
        resultSection.classList.add('fade-in');
        
        // Hide preview section
        previewSection.style.display = 'none';
    }

    // File input change event
    fileInput.addEventListener('change', function() {
        // Reset sections when new file is selected
        previewSection.style.display = 'none';
        resultSection.style.display = 'none';
        alertContainer.innerHTML = '';
    });

    // Download button click tracking
    downloadBtn.addEventListener('click', function() {
        showAlert('Đang tải xuống file...', 'info');
    });
});
