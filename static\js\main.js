// Main JavaScript for Question Shuffler

document.addEventListener('DOMContentLoaded', function() {
    const uploadForm = document.getElementById('uploadForm');
    const fileInput = document.getElementById('fileInput');
    const previewBtn = document.getElementById('previewBtn');
    const loading = document.getElementById('loading');
    const alertContainer = document.getElementById('alertContainer');
    const previewSection = document.getElementById('previewSection');
    const previewContent = document.getElementById('previewContent');
    const resultSection = document.getElementById('resultSection');
    const resultContent = document.getElementById('resultContent');
    const downloadBtn = document.getElementById('downloadBtn');

    // Các elements mới cho tùy chọn
    const questionOptions = document.getElementById('questionOptions');
    const shuffleAllRadio = document.getElementById('shuffleAll');
    const shuffleCustomRadio = document.getElementById('shuffleCustom');
    const customOptions = document.getElementById('customOptions');
    const questionCountInput = document.getElementById('questionCount');
    const startFromInput = document.getElementById('startFrom');
    const maxQuestionsSpan = document.getElementById('maxQuestions');

    let totalQuestions = 0;

    // Event listeners cho radio buttons
    shuffleCustomRadio.addEventListener('change', function() {
        if (this.checked) {
            customOptions.style.display = 'block';
        }
    });

    shuffleAllRadio.addEventListener('change', function() {
        if (this.checked) {
            customOptions.style.display = 'none';
        }
    });

    // Validation cho custom options
    questionCountInput.addEventListener('input', function() {
        const value = parseInt(this.value);
        const startFrom = parseInt(startFromInput.value) || 1;

        if (value > totalQuestions) {
            this.value = totalQuestions;
        }

        if (startFrom + value - 1 > totalQuestions) {
            startFromInput.value = Math.max(1, totalQuestions - value + 1);
        }
    });

    startFromInput.addEventListener('input', function() {
        const value = parseInt(this.value);
        const questionCount = parseInt(questionCountInput.value) || 1;

        if (value < 1) {
            this.value = 1;
        }

        if (value > totalQuestions) {
            this.value = totalQuestions;
        }

        if (value + questionCount - 1 > totalQuestions) {
            questionCountInput.value = Math.max(1, totalQuestions - value + 1);
        }
    });

    // Show alert function
    function showAlert(message, type = 'danger') {
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show fade-in`;
        alertDiv.innerHTML = `
            <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-triangle'} me-2"></i>
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        alertContainer.innerHTML = '';
        alertContainer.appendChild(alertDiv);
        
        // Auto dismiss after 5 seconds
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, 5000);
    }

    // Show loading
    function showLoading() {
        loading.style.display = 'block';
        previewSection.style.display = 'none';
        resultSection.style.display = 'none';
        alertContainer.innerHTML = '';
    }

    // Hide loading
    function hideLoading() {
        loading.style.display = 'none';
    }

    // Validate file
    function validateFile() {
        const file = fileInput.files[0];
        if (!file) {
            showAlert('Vui lòng chọn file Word (.docx)');
            return false;
        }

        if (!file.name.toLowerCase().endsWith('.docx')) {
            showAlert('File phải có định dạng .docx');
            return false;
        }

        if (file.size > 16 * 1024 * 1024) { // 16MB
            showAlert('File quá lớn. Kích thước tối đa là 16MB');
            return false;
        }

        return true;
    }

    // Preview questions
    previewBtn.addEventListener('click', function() {
        if (!validateFile()) return;

        showLoading();
        
        const formData = new FormData();
        formData.append('file', fileInput.files[0]);

        fetch('/preview', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            hideLoading();
            
            if (data.success) {
                displayPreview(data);
                showAlert(`Tìm thấy ${data.total_questions} câu hỏi trong file`, 'success');
            } else {
                showAlert(data.error || 'Có lỗi xảy ra khi xem trước file');
            }
        })
        .catch(error => {
            hideLoading();
            showAlert('Lỗi kết nối: ' + error.message);
        });
    });

    // Display preview
    function displayPreview(data) {
        totalQuestions = data.total_questions;
        maxQuestionsSpan.textContent = totalQuestions;
        questionCountInput.max = totalQuestions;
        startFromInput.max = totalQuestions;
        questionCountInput.value = totalQuestions;

        // Hiển thị tùy chọn
        questionOptions.style.display = 'block';

        let html = `
            <div class="mb-3">
                <strong>Tổng số câu hỏi:</strong> ${data.total_questions}
                ${data.has_more ? '<br><em>(Hiển thị 5 câu đầu tiên)</em>' : ''}
            </div>
        `;

        data.preview.forEach(question => {
            html += `
                <div class="question-preview">
                    <div class="question-text">${question.text}</div>
                    <div class="answers">
                        ${question.answers.map(answer => `
                            <div class="answer-option ${answer.startsWith(question.correct_answer + '.') ? 'correct-answer' : ''}">
                                ${answer}
                            </div>
                        `).join('')}
                    </div>
                </div>
            `;
        });

        previewContent.innerHTML = html;
        previewSection.style.display = 'block';
        previewSection.classList.add('fade-in');
    }

    // Upload and shuffle
    uploadForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        if (!validateFile()) return;

        showLoading();
        
        const formData = new FormData();
        formData.append('file', fileInput.files[0]);

        // Thêm thông tin về tùy chọn tráo
        if (shuffleCustomRadio.checked) {
            const questionCount = parseInt(questionCountInput.value) || totalQuestions;
            const startFrom = parseInt(startFromInput.value) || 1;

            formData.append('shuffle_mode', 'custom');
            formData.append('question_count', questionCount);
            formData.append('start_from', startFrom);
        } else {
            formData.append('shuffle_mode', 'all');
        }

        fetch('/upload', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            hideLoading();
            
            if (data.success) {
                displayResult(data);
                showAlert(data.message, 'success');

                // Hiển thị cảnh báo nếu có
                if (data.warning) {
                    setTimeout(() => {
                        showAlert(data.warning, 'warning');
                    }, 1000);
                }
            } else {
                showAlert(data.error || 'Có lỗi xảy ra khi xử lý file');
            }
        })
        .catch(error => {
            hideLoading();
            showAlert('Lỗi kết nối: ' + error.message);
        });
    });

    // Display result
    function displayResult(data) {
        const html = `
            <div class="text-center">
                <i class="fas fa-check-circle text-success success-icon" style="font-size: 3rem;"></i>
                <h4 class="mt-3">Tráo câu hỏi thành công!</h4>
                <p class="mb-3">${data.message}</p>
                <div class="row text-center">
                    <div class="col-md-3">
                        <div class="card bg-light">
                            <div class="card-body">
                                <h5 class="card-title">Tổng câu hỏi</h5>
                                <h3 class="text-info">${data.original_count}</h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-light">
                            <div class="card-body">
                                <h5 class="card-title">Có đáp án đúng</h5>
                                <h3 class="text-success">${data.questions_with_answer || 'N/A'}</h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-light">
                            <div class="card-body">
                                <h5 class="card-title">Không gạch chân</h5>
                                <h3 class="text-warning">${data.questions_without_answer || 0}</h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="card bg-light">
                            <div class="card-body">
                                <h5 class="card-title">Đã tráo</h5>
                                <h3 class="text-primary">${data.shuffled_count || data.original_count}</h3>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="mt-3">
                    <small class="text-muted">File: ${data.filename}</small>
                </div>
            </div>
        `;

        resultContent.innerHTML = html;
        downloadBtn.href = data.download_url;
        downloadBtn.style.display = 'inline-block';
        resultSection.style.display = 'block';
        resultSection.classList.add('fade-in');
        
        // Hide preview section
        previewSection.style.display = 'none';
    }

    // File input change event
    fileInput.addEventListener('change', function() {
        // Reset sections when new file is selected
        previewSection.style.display = 'none';
        resultSection.style.display = 'none';
        questionOptions.style.display = 'none';
        alertContainer.innerHTML = '';

        // Reset form
        shuffleAllRadio.checked = true;
        customOptions.style.display = 'none';
        totalQuestions = 0;
    });

    // Download button click tracking
    downloadBtn.addEventListener('click', function() {
        showAlert('Đang tải xuống file...', 'info');
    });
});
